# shell.nix (GUI対応版)
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = [
    # 1. C++開発の基本ツール
    pkgs.xmake
    pkgs.clang_16
    pkgs.pkg-config

    # 2. 既存のネットワーク関連ライブラリ
    pkgs.curl
    pkgs.openssl
    pkgs.zlib
    pkgs.cacert

    # 3. GUI (ImGui) に必要なシステムライブラリを追加
    pkgs.glfw        # ウィンドウ管理
    pkgs.glew        # OpenGL拡張機能の読み込み
    pkgs.mesa    # OpenGL開発用ヘッダ
    pkgs.xorg.libX11 # 以下、Linuxデスクトップ環境で必要
    pkgs.xorg.libXcursor
    pkgs.xorg.libXrandr
    pkgs.xorg.libXi
    pkgs.xorg.libXinerama

    pkgs.nlohmann_json
    pkgs.cpr

    # 4. WebSocket関連ライブラリ
    pkgs.boost
    pkgs.websocketpp

    # 5. 通知関連ライブラリ
    pkgs.libnotify
    pkgs.glib.dev
    pkgs.gobject-introspection
  ];

  # (shellHookは変更なし)
  shellHook = ''
    echo ""
    echo "Heart Rate Viewer C++ (GUI) 開発環境へようこそ！"
    echo "------------------------------------------------"
    echo "  xmake run     - ビルドして実行"
    echo "------------------------------------------------"
    echo ""
  '';
}