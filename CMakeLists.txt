cmake_minimum_required(VERSION 3.16)
project(HeartRateViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread)

# Find nlohmann_json
find_package(nlohmann_json REQUIRED)

# WebSocket++ is header-only, so we just need to find it
find_path(WEBSOCKETPP_INCLUDE_DIR websocketpp/config/asio_client.hpp)

# Find GUI libraries
find_package(glfw3 REQUIRED)
find_package(OpenGL REQUIRED)
find_package(GLEW REQUIRED)

# Find ImGui (assuming it's installed via package manager)
find_package(imgui REQUIRED)

# Find libnotify for system notifications
pkg_check_modules(LIBNOTIFY REQUIRED libnotify)
pkg_check_modules(GLIB REQUIRED glib-2.0)

# Create simple console executable first
add_executable(heart-rate-viewer-simple
    src/simple_main.cpp
    src/HeartRateMonitor.cpp
)

# Include directories
target_include_directories(heart-rate-viewer-simple PRIVATE
    src
    ${WEBSOCKETPP_INCLUDE_DIR}
)

# Link libraries
target_link_libraries(heart-rate-viewer-simple
    OpenSSL::SSL
    OpenSSL::Crypto
    Boost::system
    Boost::thread
    nlohmann_json::nlohmann_json
    pthread
    dl
)

# Create GUI executable with notification support
add_executable(heart-rate-viewer
    src/main.cpp
    src/HeartRateMonitor.cpp
)

# Include directories for GUI version
target_include_directories(heart-rate-viewer PRIVATE
    src
    ${WEBSOCKETPP_INCLUDE_DIR}
    ${LIBNOTIFY_INCLUDE_DIRS}
    ${GLIB_INCLUDE_DIRS}
)

# Compile definitions for GUI version
target_compile_definitions(heart-rate-viewer PRIVATE
    ${LIBNOTIFY_CFLAGS_OTHER}
    ${GLIB_CFLAGS_OTHER}
)

# Link libraries for GUI version
target_link_libraries(heart-rate-viewer
    OpenSSL::SSL
    OpenSSL::Crypto
    Boost::system
    Boost::thread
    nlohmann_json::nlohmann_json
    glfw
    OpenGL::GL
    GLEW::GLEW
    ${LIBNOTIFY_LIBRARIES}
    ${GLIB_LIBRARIES}
    pthread
    dl
)
