-- xmake.lua (NixOS対応版)
add_rules("mode.debug", "mode.release")
set_project("HeartRateViewer")
set_version("1.0.0")

set_languages("cxx17")

-- NixOSではシステムパッケージを使用
add_requires("glfw", "opengl", "imgui[glfw_opengl3]", "websocketpp")

-- GUI版（後で実装）
target("heart-rate-viewer") do
    set_kind("binary")
    add_files("src/main.cpp", "src/HeartRateMonitor.cpp")
    add_includedirs("src")

    if is_plat("linux") then
        add_packages("openssl", "boost", "glfw", "opengl", "imgui", "websocketpp")
        add_syslinks("GL", "GLEW", "glfw", "ssl", "crypto", "X11", "Xrandr", "Xinerama", "Xcursor", "pthread", "dl")
        -- Add libnotify for system notifications
        add_syslinks("notify", "glib-2.0", "gobject-2.0")
        add_includedirs("/nix/store/*/include")
        -- Add pkg-config paths for libnotify
        add_includedirs("/usr/include/libnotify", "/usr/include/glib-2.0", "/usr/lib/x86_64-linux-gnu/glib-2.0/include")
    end
end
