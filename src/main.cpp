#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <string>
#include <atomic>
#include <vector>
#include <deque>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <cstring>
#include <cstdlib>

// Notification includes
#include <libnotify/notify.h>

// ImGui includes
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>

// OpenGL includes
#include <GL/glew.h>
#include <GLFW/glfw3.h>

// WebSocket includes
#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>

// JSON includes
#include <nlohmann/json.hpp>

#include "HeartRateMonitor.h"

// Global variables
std::atomic<int> g_currentHeartRate{0};
std::atomic<bool> g_isConnected{false};
std::string g_connectionStatus = "Disconnected";
std::string g_accessToken = "";

// Heart rate history for graph
std::deque<float> g_heartRateHistory;
const size_t MAX_HISTORY_SIZE = 100;

// Notification settings
static int g_highHeartRateThreshold = 150;  // Default threshold for high heart rate
static bool g_notificationsEnabled = true;
static std::chrono::steady_clock::time_point g_lastNotificationTime;
static const std::chrono::minutes NOTIFICATION_COOLDOWN{2}; // 2 minutes cooldown between notifications

// Error callback for GLFW
static void glfw_error_callback(int error, const char* description)
{
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

// Get config file path (in user's home directory)
std::string GetConfigFilePath()
{
    const char* home = getenv("HOME");
    if (home)
    {
        return std::string(home) + "/.heart-rate-viewer.conf";
    }
    return "heart-rate-viewer.conf"; // Fallback to current directory
}

// Function to save token to config file
void SaveTokenToConfig(const std::string& token)
{
    std::string configPath = GetConfigFilePath();
    std::ifstream inFile(configPath);
    std::stringstream buffer;
    std::string line;
    bool tokenLineFound = false;

    // Read existing file and modify token line
    while (std::getline(inFile, line))
    {
        if (line.find("AccessToken=") == 0)
        {
            buffer << "AccessToken=" << token << "\n";
            tokenLineFound = true;
        }
        else
        {
            buffer << line << "\n";
        }
    }
    inFile.close();

    // If token line wasn't found, add it
    if (!tokenLineFound)
    {
        buffer << "AccessToken=" << token << "\n";
    }

    // Write back to file
    std::ofstream outFile(configPath);
    outFile << buffer.str();
    outFile.close();

    std::cout << "Token saved to: " << configPath << std::endl;
}

// Function to load token from config file
std::string LoadTokenFromConfig()
{
    std::string configPath = GetConfigFilePath();
    std::ifstream inFile(configPath);
    std::string line;

    while (std::getline(inFile, line))
    {
        if (line.find("AccessToken=") == 0)
        {
            std::cout << "Token loaded from: " << configPath << std::endl;
            return line.substr(12); // Remove "AccessToken=" prefix
        }
    }
    return "";
}

// Function to load notification settings from config file
void LoadNotificationSettings()
{
    std::string configPath = GetConfigFilePath();
    std::ifstream inFile(configPath);
    std::string line;

    while (std::getline(inFile, line))
    {
        if (line.find("NotificationsEnabled=") == 0)
        {
            g_notificationsEnabled = (line.substr(21) == "true");
        }
        else if (line.find("HeartRateThreshold=") == 0)
        {
            try
            {
                g_highHeartRateThreshold = std::stoi(line.substr(19));
            }
            catch (const std::exception& e)
            {
                std::cerr << "Error parsing heart rate threshold: " << e.what() << std::endl;
            }
        }
    }
}

// Function to save notification settings to config file
void SaveNotificationSettings()
{
    std::string configPath = GetConfigFilePath();
    std::ifstream inFile(configPath);
    std::stringstream buffer;
    std::string line;
    bool notificationsLineFound = false;
    bool thresholdLineFound = false;

    // Read existing file and modify notification lines
    while (std::getline(inFile, line))
    {
        if (line.find("NotificationsEnabled=") == 0)
        {
            buffer << "NotificationsEnabled=" << (g_notificationsEnabled ? "true" : "false") << "\n";
            notificationsLineFound = true;
        }
        else if (line.find("HeartRateThreshold=") == 0)
        {
            buffer << "HeartRateThreshold=" << g_highHeartRateThreshold << "\n";
            thresholdLineFound = true;
        }
        else
        {
            buffer << line << "\n";
        }
    }
    inFile.close();

    // If notification lines weren't found, add them
    if (!notificationsLineFound)
    {
        buffer << "NotificationsEnabled=" << (g_notificationsEnabled ? "true" : "false") << "\n";
    }
    if (!thresholdLineFound)
    {
        buffer << "HeartRateThreshold=" << g_highHeartRateThreshold << "\n";
    }

    // Write back to file
    std::ofstream outFile(configPath);
    outFile << buffer.str();
    outFile.close();
}

// Function to show system notification
void ShowHighHeartRateNotification(int heartRate)
{
    if (!g_notificationsEnabled)
        return;

    // Check cooldown period
    auto now = std::chrono::steady_clock::now();
    if (now - g_lastNotificationTime < NOTIFICATION_COOLDOWN)
        return;

    // Initialize libnotify if not already done
    static bool notifyInitialized = false;
    if (!notifyInitialized)
    {
        if (!notify_init("Heart Rate Monitor"))
        {
            std::cerr << "Failed to initialize libnotify" << std::endl;
            return;
        }
        notifyInitialized = true;
    }

    // Create notification message
    std::string title = "High Heart Rate Alert!";
    std::string message = "Your heart rate is " + std::to_string(heartRate) + " BPM";

    // Create and show notification
    NotifyNotification* notification = notify_notification_new(
        title.c_str(),
        message.c_str(),
        "dialog-warning" // Icon name
    );

    if (notification)
    {
        // Set urgency to critical for high heart rate
        notify_notification_set_urgency(notification, NOTIFY_URGENCY_CRITICAL);

        // Set timeout (5 seconds)
        notify_notification_set_timeout(notification, 5000);

        GError* error = nullptr;
        if (!notify_notification_show(notification, &error))
        {
            if (error)
            {
                std::cerr << "Failed to show notification: " << error->message << std::endl;
                g_error_free(error);
            }
        }
        else
        {
            std::cout << "High heart rate notification shown: " << heartRate << " BPM" << std::endl;
            g_lastNotificationTime = now;
        }

        g_object_unref(notification);
    }
}

int main()
{
    // Setup GLFW
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit())
        return -1;

    // GL 3.0 + GLSL 130
    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 0);

    // Create window with graphics context
    GLFWwindow* window = glfwCreateWindow(800, 600, "Heart Rate Viewer", nullptr, nullptr);
    if (window == nullptr)
        return -1;

    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // Enable vsync

    // Initialize GLEW
    if (glewInit() != GLEW_OK)
    {
        std::cerr << "Failed to initialize GLEW" << std::endl;
        return -1;
    }

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // Create heart rate monitor
    std::unique_ptr<HeartRateMonitor> monitor;

    // Variables for heart rate history tracking
    static int lastHeartRate = 0;
    auto lastUpdateTime = std::chrono::steady_clock::now();

    // Load saved token and notification settings
    static char token_buffer[256] = "";
    std::string savedToken = LoadTokenFromConfig();
    if (!savedToken.empty())
    {
        strncpy(token_buffer, savedToken.c_str(), sizeof(token_buffer) - 1);
        token_buffer[sizeof(token_buffer) - 1] = '\0';
    }

    // Load notification settings
    LoadNotificationSettings();

    // Main loop
    while (!glfwWindowShouldClose(window))
    {
        glfwPollEvents();

        // Update heart rate history
        int currentHR = g_currentHeartRate.load();
        auto currentTime = std::chrono::steady_clock::now();
        auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastUpdateTime);

        if (currentHR != lastHeartRate && currentHR > 0)
        {
            g_heartRateHistory.push_back(static_cast<float>(currentHR));
            if (g_heartRateHistory.size() > MAX_HISTORY_SIZE)
            {
                g_heartRateHistory.pop_front();
            }

            // Check for high heart rate and show notification
            if (currentHR >= g_highHeartRateThreshold)
            {
                ShowHighHeartRateNotification(currentHR);
            }

            lastHeartRate = currentHR;
            lastUpdateTime = currentTime;
        }

        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // Main window
        ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);
        ImGui::Begin("Heart Rate Monitor", nullptr, ImGuiWindowFlags_NoCollapse);

        // Connection section
        ImGui::Text("Connection Settings");
        ImGui::Separator();

        // Access token input
        ImGui::InputText("Access Token", token_buffer, sizeof(token_buffer), ImGuiInputTextFlags_Password);

        if (ImGui::Button("Connect"))
        {
            if (strlen(token_buffer) > 0)
            {
                g_accessToken = std::string(token_buffer);
                SaveTokenToConfig(g_accessToken); // Save token when connecting
                monitor = std::make_unique<HeartRateMonitor>(g_accessToken);
                monitor->start();
            }
        }

        ImGui::SameLine();

        if (ImGui::Button("Disconnect"))
        {
            if (monitor)
            {
                monitor->stop();
                monitor.reset();
            }
        }

        // Connection status with color coding
        ImGui::Text("Status: ");
        ImGui::SameLine();
        if (g_isConnected)
        {
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "%s", g_connectionStatus.c_str());
        }
        else
        {
            ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "%s", g_connectionStatus.c_str());
        }

        ImGui::Spacing();
        ImGui::Separator();

        // Heart rate display section
        ImGui::Text("Current Heart Rate");

        // Large heart rate display
        ImGui::PushFont(io.Fonts->Fonts[0]);
        ImGui::SetWindowFontScale(4.0f);

        if (g_isConnected && g_currentHeartRate > 0)
        {
            // Color coding based on heart rate zones
            int hr = g_currentHeartRate.load();
            ImVec4 color;
            if (hr < 60) color = ImVec4(0.0f, 0.5f, 1.0f, 1.0f);      // Blue - Low
            else if (hr < 100) color = ImVec4(0.0f, 1.0f, 0.0f, 1.0f); // Green - Normal
            else if (hr < 150) color = ImVec4(1.0f, 1.0f, 0.0f, 1.0f); // Yellow - Elevated
            else color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);               // Red - High

            ImGui::TextColored(color, "%d BPM", hr);
        }
        else
        {
            ImGui::TextColored(ImVec4(0.5f, 0.5f, 0.5f, 1.0f), "-- BPM");
        }

        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();

        // Notification settings section
        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Text("Notification Settings");

        ImGui::Checkbox("Enable Notifications", &g_notificationsEnabled);

        static int previousThreshold = g_highHeartRateThreshold;
        static bool previousNotificationsEnabled = g_notificationsEnabled;

        ImGui::SliderInt("High Heart Rate Threshold", &g_highHeartRateThreshold, 100, 200, "%d BPM");

        if (ImGui::IsItemHovered())
        {
            ImGui::SetTooltip("You will receive notifications when your heart rate exceeds this threshold");
        }

        // Save settings if they changed
        if (g_highHeartRateThreshold != previousThreshold || g_notificationsEnabled != previousNotificationsEnabled)
        {
            SaveNotificationSettings();
            previousThreshold = g_highHeartRateThreshold;
            previousNotificationsEnabled = g_notificationsEnabled;
        }

        // Heart rate history graph
        if (!g_heartRateHistory.empty())
        {
            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Text("Heart Rate History");

            std::vector<float> values(g_heartRateHistory.begin(), g_heartRateHistory.end());

            // Calculate dynamic Y-axis range based on history
            float minValue = *std::min_element(values.begin(), values.end());
            float maxValue = *std::max_element(values.begin(), values.end());

            // Add some padding to the range for better visualization
            float padding = (maxValue - minValue) * 0.1f; // 10% padding
            if (padding < 5.0f) padding = 5.0f; // Minimum padding of 5 BPM

            float graphMin = std::max(0.0f, minValue - padding);
            float graphMax = maxValue + padding;

            // Display min/max info
            ImGui::Text("Range: %.0f - %.0f BPM", minValue, maxValue);

            ImGui::PlotLines("##HeartRateGraph", values.data(), values.size(), 0, nullptr, graphMin, graphMax, ImVec2(0, 120));
        }

        ImGui::End();

        // Rendering
        ImGui::Render();
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);
        glViewport(0, 0, display_w, display_h);
        glClearColor(0.45f, 0.55f, 0.60f, 1.00f);
        glClear(GL_COLOR_BUFFER_BIT);
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // Cleanup
    if (monitor)
    {
        monitor->stop();
    }

    // Cleanup libnotify
    notify_uninit();

    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}
