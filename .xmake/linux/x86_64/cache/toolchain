{
    fpc_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    ["tool_target_heart-rate-viewer_linux_x86_64_ld"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolname = "gxx",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    cross_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = false
    },
    yasm_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    tool_platform_linux_x86_64_cc = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    swift_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    cuda_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    nasm_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    tool_platform_linux_x86_64_ld = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolname = "gxx",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    nim_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = false
    },
    go_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    rust_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    gcc_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = {
            name = "gcc",
            program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
        }
    },
    ["tool_target_heart-rate-viewer_linux_x86_64_cxx"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolname = "gxx",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    fasm_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    gfortran_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    },
    envs_arch_x86_64_plat_linux = {
        __global = true,
        arch = "x86_64",
        plat = "linux",
        __checked = true
    }
}