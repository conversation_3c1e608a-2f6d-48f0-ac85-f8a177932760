{
    gfortran_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    rust_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    ["tool_target_heart-rate-viewer_linux_x86_64_cxx"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolname = "gxx",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    cuda_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    go_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    nim_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = false,
        arch = "x86_64"
    },
    cross_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = false,
        arch = "x86_64"
    },
    tool_platform_linux_x86_64_ld = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolname = "gxx",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    yasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    nasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    swift_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    tool_platform_linux_x86_64_cc = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    envs_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    ["tool_target_heart-rate-viewer_linux_x86_64_ld"] = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolname = "gxx",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    fpc_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    tool_platform_linux_x86_64_cxx = {
        program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        toolname = "gxx",
        toolchain_info = {
            plat = "linux",
            name = "envs",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64"
        }
    },
    gcc_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = {
            program = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
            name = "gcc"
        },
        arch = "x86_64"
    },
    fasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    }
}