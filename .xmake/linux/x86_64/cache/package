{
    opengl = {
        version = "4.5",
        links = "OpenGL",
        __requirestr = "opengl",
        sysincludedirs = "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include",
        __enabled = true,
        shared = true,
        linkdirs = "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib",
        libfiles = "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
    },
    websocketpp = {
        links = {
            "boost_filesystem",
            "boost_thread",
            "boost_date_time",
            "boost_container",
            "boost_chrono",
            "boost_atomic"
        },
        sysincludedirs = {
            "/home/<USER>/.xmake/packages/w/websocketpp/0.8.2/a995c8150a01414fa906fc12e9273dbe/include",
            "/home/<USER>/.xmake/packages/a/asio/1.32.0/534f961af8274a53a3ee065d110c24f3/include",
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/include"
        },
        envs = {
            PATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/Scripts",
                "/home/<USER>/.xmake/packages/n/ninja/v1.12.1/12c84655558847c09203ef9d0105b20d/bin",
                "/home/<USER>/.xmake/packages/c/cmake/4.0.3/b686e3b7911246e2bfa1b52ae27762fe/bin"
            },
            SSL_CERT_FILE = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20/cacert.pem"
            },
            PYTHONPATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/lib/python3.13/site-packages"
            },
            SSL_CERT_DIR = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20"
            }
        },
        libfiles = {
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_filesystem.a",
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_thread.a",
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_date_time.a",
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_container.a",
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_chrono.a",
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_atomic.a"
        },
        version = "0.8.2",
        syslinks = {
            "pthread",
            "dl"
        },
        defines = {
            "ASIO_STANDALONE",
            "_WEBSOCKETPP_CPP11_STL_"
        },
        linkdirs = "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib",
        __enabled = true,
        installdir = "/home/<USER>/.xmake/packages/w/websocketpp/0.8.2/a995c8150a01414fa906fc12e9273dbe",
        __requirestr = "websocketpp"
    },
    imgui = {
        links = {
            "imgui",
            "glfw",
            "OpenGL",
            "Xrandr",
            "Xinerama",
            "Xcursor",
            "Xrender",
            "X11",
            "Xi",
            "Xfixes",
            "Xext",
            "xcb",
            "Xau"
        },
        license = "MIT",
        envs = {
            LD_LIBRARY_PATH = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib"
            },
            SSL_CERT_DIR = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20"
            },
            PATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/Scripts",
                "/home/<USER>/.xmake/packages/n/ninja/v1.12.1/12c84655558847c09203ef9d0105b20d/bin",
                "/home/<USER>/.xmake/packages/c/cmake/4.0.3/b686e3b7911246e2bfa1b52ae27762fe/bin",
                "/home/<USER>/.xmake/packages/p/pkg-config/0.29.2/a0aba256d7674c1bbee15f0ee1534e92/bin",
                "/home/<USER>/.xmake/packages/p/pkg-config/0.29.2/a0aba256d7674c1bbee15f0ee1534e92/bin"
            },
            PYTHONPATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/lib/python3.13/site-packages"
            },
            SSL_CERT_FILE = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20/cacert.pem"
            }
        },
        libfiles = {
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/lib/libimgui.a",
            "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so",
            "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so",
            "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so",
            "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so",
            "/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib/libXrender.so",
            "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib/libX11.so",
            "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so",
            "/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib/libXfixes.so",
            "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so",
            "/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib/libxcb.so",
            "/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib/libXau.so"
        },
        version = "v1.92.0",
        static = true,
        sysincludedirs = {
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp",
            "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include",
            "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
            "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include",
            "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include",
            "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include",
            "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include",
            "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include",
            "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include",
            "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include",
            "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include",
            "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include",
            "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include"
        },
        linkdirs = {
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/lib",
            "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib",
            "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib",
            "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib",
            "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib",
            "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib",
            "/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib",
            "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib",
            "/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib",
            "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib",
            "/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib",
            "/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib"
        },
        __enabled = true,
        installdir = "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27",
        __requirestr = "imgui[glfw_opengl3]"
    },
    glfw = {
        links = {
            "glfw",
            "OpenGL",
            "Xrandr",
            "Xinerama",
            "Xcursor",
            "Xrender",
            "X11",
            "Xi",
            "Xfixes",
            "Xext",
            "xcb",
            "Xau"
        },
        libfiles = {
            "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so",
            "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so",
            "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so",
            "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so",
            "/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib/libXrender.so",
            "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib/libX11.so",
            "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so",
            "/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib/libXfixes.so",
            "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so",
            "/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib/libxcb.so",
            "/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib/libXau.so"
        },
        sysincludedirs = {
            "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include",
            "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
            "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include",
            "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include",
            "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include",
            "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include",
            "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include",
            "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include",
            "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include",
            "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include",
            "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include",
            "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include"
        },
        __enabled = true,
        envs = {
            LD_LIBRARY_PATH = {
                "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/lib"
            },
            SSL_CERT_DIR = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20"
            },
            PATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/bin",
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/Scripts",
                "/home/<USER>/.xmake/packages/n/ninja/v1.12.1/12c84655558847c09203ef9d0105b20d/bin",
                "/home/<USER>/.xmake/packages/c/cmake/4.0.3/b686e3b7911246e2bfa1b52ae27762fe/bin",
                "/home/<USER>/.xmake/packages/p/pkg-config/0.29.2/a0aba256d7674c1bbee15f0ee1534e92/bin",
                "/home/<USER>/.xmake/packages/p/pkg-config/0.29.2/a0aba256d7674c1bbee15f0ee1534e92/bin"
            },
            PYTHONPATH = {
                "/home/<USER>/.xmake/packages/p/python/3.13.2/7dbc4c2192884093912e9407517d19cf/lib/python3.13/site-packages"
            },
            SSL_CERT_FILE = {
                "/home/<USER>/.xmake/packages/c/ca-certificates/20250131/e9ba281ae183402a80dc072d4d571c20/cacert.pem"
            }
        },
        linkdirs = {
            "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib",
            "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib",
            "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib",
            "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib",
            "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib",
            "/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib",
            "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib",
            "/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib",
            "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib",
            "/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib",
            "/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib"
        },
        __requirestr = "glfw"
    }
}