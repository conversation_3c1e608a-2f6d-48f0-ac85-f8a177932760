{
    cmdlines = {
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake ",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake ",
        "xmake ",
        "xmake lua /nix/store/73kjdrfaqvg707ywq1zvkdds3iy41ifc-xmake-3.0.0/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/73kjdrfaqvg707ywq1zvkdds3iy41ifc-xmake-3.0.0/share/xmake/actions/build/cleaner.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake ",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake ",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake ",
        "xmake ",
        "xmake lua /nix/store/73kjdrfaqvg707ywq1zvkdds3iy41ifc-xmake-3.0.0/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/73kjdrfaqvg707ywq1zvkdds3iy41ifc-xmake-3.0.0/share/xmake/actions/build/cleaner.lua",
        "xmake run",
        "xmake check",
        "xmake ",
        "xmake run",
        "xmake run",
        "xmake run",
        "xmake run",
        "xmake run",
        "xmake check",
        "xmake check",
        "xmake run",
        "xmake run",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake check"
    }
}