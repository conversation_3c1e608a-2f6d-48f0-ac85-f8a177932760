{
    ["core.tools.gcc.has_ldflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0"] = {
            ["-rpath-link"] = true,
            ["--error-handling-script"] = true,
            ["--verbose"] = true,
            ["-Bgroup"] = true,
            ["-rpath"] = true,
            ["-nostdlib"] = true,
            ["-c"] = true,
            ["--no-warn-mismatch"] = true,
            ["--whole-archive"] = true,
            ["-debug"] = true,
            ["--print-memory-usage"] = true,
            ["--output"] = true,
            ["-flto"] = true,
            ["--split-by-file"] = true,
            ["--no-warn-execstack"] = true,
            ["--out-implib"] = true,
            ["--no-as-needed"] = true,
            ["--print-gc-sections"] = true,
            ["-h"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["--sort-section"] = true,
            ["-Tbss"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--no-define-common"] = true,
            ["-Tldata-segment"] = true,
            ["--force-group-allocation"] = true,
            ["--dynamic-list-data"] = true,
            ["--enable-linker-version"] = true,
            ["-dp"] = true,
            ["--allow-multiple-definition"] = true,
            ["--stats"] = true,
            ["--no-export-dynamic"] = true,
            ["--start-group"] = true,
            ["--check-sections"] = true,
            ["--no-error-execstack"] = true,
            ["-e"] = true,
            ["--default-imported-symver"] = true,
            ["-Bno-symbolic"] = true,
            ["-R"] = true,
            ["--no-undefined"] = true,
            ["--omagic"] = true,
            ["--no-print-map-discarded"] = true,
            ["-Ur"] = true,
            ["--version"] = true,
            ["--no-gc-sections"] = true,
            ["--warn-once"] = true,
            ["--print-map-locals"] = true,
            ["--just-symbols"] = true,
            ["--discard-all"] = true,
            ["--filter"] = true,
            ["--no-demangle"] = true,
            ["-u"] = true,
            ["--push-state"] = true,
            ["-P"] = true,
            ["--pic-executable"] = true,
            ["-Map"] = true,
            ["--no-omagic"] = true,
            ["--oformat"] = true,
            ["--version-exports-section"] = true,
            ["--section-ordering-file"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--warn-common"] = true,
            ["--remap-inputs"] = true,
            ["-b"] = true,
            ["--defsym"] = true,
            ["-plugin-save-temps"] = true,
            ["--gc-sections"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--print-map-discarded"] = true,
            ["--dependency-file"] = true,
            ["--no-print-map-locals"] = true,
            ["--disable-new-dtags"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--as-needed"] = true,
            ["-dT"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["-Ttext-segment"] = true,
            ["-l"] = true,
            ["--wrap"] = true,
            ["-soname"] = true,
            ["-EB"] = true,
            ["--warn-alternate-em"] = true,
            ["--undefined-version"] = true,
            ["-g"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--no-map-whole-files"] = true,
            ["--library"] = true,
            ["--script"] = true,
            ["--map-whole-files"] = true,
            ["--cref"] = true,
            ["--relocatable"] = true,
            ["--split-by-reloc"] = true,
            ["--version-script"] = true,
            ["-Ttext"] = true,
            ["-L"] = true,
            ["-plugin"] = true,
            ["--orphan-handling"] = true,
            ["--library-path"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--task-link"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--warn-textrel"] = true,
            ["--rosegment"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["-static"] = true,
            ["--end-group"] = true,
            ["--unique"] = true,
            ["--print-sysroot"] = true,
            ["--warn-execstack"] = true,
            ["-z"] = true,
            ["--no-relax"] = true,
            ["-o"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--error-execstack"] = true,
            ["--no-ctf-variables"] = true,
            ["--disable-linker-version"] = true,
            ["-no-pie"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--discard-none"] = true,
            ["--mri-script"] = true,
            ["--no-whole-archive"] = true,
            ["--no-strip-discarded"] = true,
            ["--default-symver"] = true,
            ["--trace-symbol"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--no-error-rwx-segments"] = true,
            ["--print-output-format"] = true,
            ["--force-exe-suffix"] = true,
            ["--export-dynamic"] = true,
            ["-O"] = true,
            ["--auxiliary"] = true,
            ["--image-base"] = true,
            ["--strip-discarded"] = true,
            ["--help"] = true,
            ["--emit-relocs"] = true,
            ["-qmagic"] = true,
            ["--warn-section-align"] = true,
            ["-fini"] = true,
            ["--format"] = true,
            ["-EL"] = true,
            ["--error-rwx-segments"] = true,
            ["--eh-frame-hdr"] = true,
            ["--no-check-sections"] = true,
            ["--fatal-warnings"] = true,
            ["--nmagic"] = true,
            ["--no-warnings"] = true,
            ["-Trodata-segment"] = true,
            ["--warn-multiple-gp"] = true,
            ["-a"] = true,
            ["--no-keep-memory"] = true,
            ["-V"] = true,
            ["--no-dynamic-linker"] = true,
            ["--no-fatal-warnings"] = true,
            ["--strip-debug"] = true,
            ["--dynamic-linker"] = true,
            ["--no-rosegment"] = true,
            ["--print-map"] = true,
            ["--gc-keep-exported"] = true,
            ["--no-print-gc-sections"] = true,
            ["--trace"] = true,
            ["--demangle"] = true,
            ["--spare-dynamic-tags"] = true,
            ["-assert"] = true,
            ["-init"] = true,
            ["--undefined"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["-y"] = true,
            ["-F"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["--require-defined"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--no-undefined-version"] = true,
            ["--section-start"] = true,
            ["--target-help"] = true,
            ["--architecture"] = true,
            ["-f"] = true,
            ["--entry"] = true,
            ["-Qy"] = true,
            ["--remap-inputs-file"] = true,
            ["-Bsymbolic"] = true,
            ["--gpsize"] = true,
            ["-A"] = true,
            ["--allow-shlib-undefined"] = true,
            ["-I"] = true,
            ["--sort-common"] = true,
            ["--pop-state"] = true,
            ["-plugin-opt"] = true,
            ["--enable-new-dtags"] = true,
            ["--retain-symbols-file"] = true,
            ["--strip-all"] = true,
            ["--ctf-variables"] = true,
            ["--warn-execstack-objects"] = true,
            ["--traditional-format"] = true,
            ["--relax"] = true,
            ["--discard-locals"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["-m"] = true,
            ["--warn-rwx-segments"] = true,
            ["--default-script"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--dynamic-list"] = true,
            ["-T"] = true,
            ["-Tdata"] = true,
            ["-Y"] = true,
            ["-G"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["-Bshareable"] = true
        }
    },
    find_package_linux_x86_64_fetch_package_system = {
        ["apt::libxcb-composite0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxext-dev_5a0d83a7724446099fee649c3d9afd32_release_external"] = false,
        ["pkgconfig::xproto_3a1682704c034fbf87583b32455dde94_release_external"] = {
            version = "7.0.33",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include"
            }
        },
        ["pacman::libxcb_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        libxfixes_31f0eaa885a84ef6b740f6ed04fea3e3_release_external = {
            version = "6.0.1",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include"
            },
            links = {
                "Xfixes"
            },
            libfiles = {
                "/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib/libXfixes.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib"
            }
        },
        ["pacman::libxmdcp_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_external"] = false,
        ["apt::libxcb-xfreedri0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        libxrender_75df963310db406385383085b95b222e_release_external = {
            version = "0.9.12",
            sysincludedirs = {
                "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include",
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include"
            },
            links = {
                "Xrender",
                "X11"
            },
            libfiles = {
                "/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib/libXrender.so",
                "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib/libX11.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib",
                "/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib"
            }
        },
        ["apt::xutils-dev_21ebacb0450b4b16b06b586ecc142d3f_release_external"] = false,
        ["apt::libxcb-damage0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-xtest0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-dri3-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-xvmc0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libglfw3-dev_4c3c72af70fd4599a3038fc662af9c28_release_external"] = false,
        libx11_5b3492e889e848b2b511e3f3031c5538_release_external = {
            version = "1.8.12",
            sysincludedirs = {
                "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include",
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include"
            },
            links = {
                "X11"
            },
            libfiles = {
                "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib/libX11.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib"
            }
        },
        libxrandr_a36fcab100ed449e86944351e00c0384_release_external = {
            version = "1.5.4",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include"
            },
            links = {
                "Xrandr"
            },
            libfiles = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib/libXrandr.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib"
            }
        },
        libxcb_27d2233a10b34ff5971001208db29e2c_release_external = {
            version = "1.17.0",
            sysincludedirs = {
                "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include"
            },
            links = {
                "xcb"
            },
            libfiles = {
                "/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib/libxcb.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib"
            }
        },
        libxdmcp_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_external = false,
        ["apt::x11proto-dev_3a1682704c034fbf87583b32455dde94_release_external"] = false,
        ["apt::libxcb-present-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxfixes-dev_31f0eaa885a84ef6b740f6ed04fea3e3_release_external"] = false,
        opengl_4e0143c97b65425b855ad5fd03038b6a_release_external = {
            version = "4.5",
            sysincludedirs = {
                "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include"
            },
            links = {
                "OpenGL"
            },
            libfiles = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib/libOpenGL.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib"
            }
        },
        ["pkgconfig::xorg-macros_21ebacb0450b4b16b06b586ecc142d3f_release_external"] = false,
        libxext_5a0d83a7724446099fee649c3d9afd32_release_external = {
            version = "1.3.6",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include"
            },
            links = {
                "Xext"
            },
            libfiles = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib/libXext.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib"
            }
        },
        ["apt::libxcb-sync0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-xinput-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-xevie0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxi-dev_2f225dfd5e274482ae09ba4da0c185c5_release_external"] = false,
        ["pacman::libxfixes_31f0eaa885a84ef6b740f6ed04fea3e3_release_external"] = false,
        ["apt::libxcb-randr0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-shm0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["pacman::xtrans_588d85a9c99148b8ae87dee11b39ab40_release_external"] = false,
        ["apt::libxcb-shape0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["pacman::glfw-x11_4c3c72af70fd4599a3038fc662af9c28_release_external"] = false,
        ["apt::libx11-dev_5b3492e889e848b2b511e3f3031c5538_release_external"] = false,
        libxi_2f225dfd5e274482ae09ba4da0c185c5_release_external = {
            version = "1.8.2",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include"
            },
            links = {
                "Xi"
            },
            libfiles = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib/libXi.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib"
            }
        },
        ["apt::libxinerama-dev_e539e5b3575342a89c7a931295fe40c5_release_external"] = false,
        ["apt::libxcb-xrm-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxau-dev_f406943012fc443596a2e2e23215839d_release_external"] = false,
        ["apt::xtrans-dev_588d85a9c99148b8ae87dee11b39ab40_release_external"] = false,
        ["apt::libxcb-xkb-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxrandr-dev_a36fcab100ed449e86944351e00c0384_release_external"] = false,
        ["apt::libxcb1-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        xtrans_588d85a9c99148b8ae87dee11b39ab40_release_external = false,
        ["apt::libxcb-xinerama0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["pacman::libx11_5b3492e889e848b2b511e3f3031c5538_release_external"] = false,
        ["libpthread-stubs_e6c09af522624eb1ae863fe21c4aa62c_release_external"] = false,
        ["apt::libxdmcp-dev_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_external"] = false,
        ["util-macros_21ebacb0450b4b16b06b586ecc142d3f_release_external"] = false,
        ["apt::libxcb-screensaver-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["xcb-proto_eafb62cb3f37412ebe648eb0ab4b4b35_release_external"] = false,
        libxinerama_e539e5b3575342a89c7a931295fe40c5_release_external = {
            version = "1.1.5",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include"
            },
            links = {
                "Xinerama"
            },
            libfiles = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib/libXinerama.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib"
            }
        },
        ["apt::libxcb-dpms0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-dri2-0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-record0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-glx0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxrender-dev_75df963310db406385383085b95b222e_release_external"] = false,
        ["apt::libxcursor-dev_665b359dc13241789f6ca7a586bc5127_release_external"] = false,
        ["apt::libxcb-render0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        glfw_4c3c72af70fd4599a3038fc662af9c28_release_external = {
            links = "glfw"
        },
        libxau_f406943012fc443596a2e2e23215839d_release_external = {
            version = "1.0.12",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include"
            },
            links = {
                "Xau"
            },
            libfiles = {
                "/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib/libXau.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib"
            }
        },
        ["apt::libxcb-xfixes0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["apt::libxcb-xv0-dev_27d2233a10b34ff5971001208db29e2c_release_external"] = false,
        ["pacman::libxau_f406943012fc443596a2e2e23215839d_release_external"] = false,
        libxcursor_665b359dc13241789f6ca7a586bc5127_release_external = {
            version = "1.2.3",
            sysincludedirs = {
                "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
                "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include"
            },
            links = {
                "Xcursor"
            },
            libfiles = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib/libXcursor.so"
            },
            shared = true,
            linkdirs = {
                "/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib"
            }
        }
    },
    find_program_fetch_package_xmake = {
        python3 = false,
        ["pkg-config"] = false,
        python = false,
        cmake = false,
        python2 = false,
        ninja = false
    },
    find_programver = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "14.3.0",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "14.3.0"
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    find_programver_fetch_package_system = {
        ["/run/current-system/sw/bin/cmake"] = "3.31.7",
        ["/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"] = "0.29.2"
    },
    find_program_fetch_package_system = {
        cmake = "/run/current-system/sw/bin/cmake",
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0"] = {
            ["-print-multi-lib"] = true,
            ["--param"] = true,
            ["-pipe"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-E"] = true,
            ["-print-multi-os-directory"] = true,
            ["-dumpspecs"] = true,
            ["-print-multi-directory"] = true,
            ["-S"] = true,
            ["-v"] = true,
            ["-save-temps"] = true,
            ["-dumpversion"] = true,
            ["-c"] = true,
            ["-print-multiarch"] = true,
            ["-print-search-dirs"] = true,
            ["-pie"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-dumpmachine"] = true,
            ["-Xassembler"] = true,
            ["--help"] = true,
            ["-print-sysroot"] = true,
            ["-x"] = true,
            ["-shared"] = true,
            ["-o"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["--target-help"] = true,
            ["--version"] = true,
            ["-time"] = true,
            ["-pass-exit-codes"] = true,
            ["-Xlinker"] = true,
            ["-B"] = true,
            ["-Xpreprocessor"] = true
        },
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0"] = {
            ["-print-multi-lib"] = true,
            ["--param"] = true,
            ["-pipe"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-E"] = true,
            ["-print-multi-os-directory"] = true,
            ["-dumpspecs"] = true,
            ["-print-multi-directory"] = true,
            ["-S"] = true,
            ["-v"] = true,
            ["-save-temps"] = true,
            ["-dumpversion"] = true,
            ["-c"] = true,
            ["-print-multiarch"] = true,
            ["-print-search-dirs"] = true,
            ["-pie"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-dumpmachine"] = true,
            ["-Xassembler"] = true,
            ["--help"] = true,
            ["-print-sysroot"] = true,
            ["-x"] = true,
            ["-shared"] = true,
            ["-o"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["--target-help"] = true,
            ["--version"] = true,
            ["-time"] = true,
            ["-pass-exit-codes"] = true,
            ["-Xlinker"] = true,
            ["-B"] = true,
            ["-Xpreprocessor"] = true
        }
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    },
    find_program = {
        brew = false,
        emerge = false,
        ping = "/run/current-system/sw/bin/ping",
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc",
        tar = "/nix/store/r8d70l24x06f4fgq246wz2s8zvmj6vxb-gnutar-1.35/bin/tar",
        git = "/run/current-system/sw/bin/git",
        pacman = false,
        gzip = "/nix/store/31h4wbbp0yvsrciyw46iij6r4c0wc8fp-gzip-1.14/bin/gzip",
        dpkg = false,
        nim = false,
        ["pkg-config"] = "/nix/store/rxz7i1sj19nrcmyk4sg49i2h1vfaazby-pkg-config-wrapper-0.29.2/bin/pkg-config"
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_cxx_cxflags_-m64_-fdiagnostics-color=always"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_ld__-m64 -m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_cxx_cxflags_-m64_-MMD -MF"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_cxx__-m64_-O3"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_cxx_cxflags_-m64_-DNDEBUG"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-Wno-gnu-line-marker -Werror"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc__-m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_cxx__-m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc_14.3.0_cc_cxflags_-m64_-fdiagnostics-color=always"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_cxx_cxflags_-m64_-fvisibility-inlines-hidden"] = true,
        ["linux_x86_64_/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++_14.3.0_cxx_cxflags_-m64_-Wno-gnu-line-marker -Werror"] = true
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolcc = {
        gcc = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc"
    },
    find_package_linux_x86_64_fetch_package_xmake = {
        ["xmake::glfw_4c3c72af70fd4599a3038fc662af9c28_release_3.4_external"] = false,
        ["xmake::websocketpp_a995c8150a01414fa906fc12e9273dbe_release_0.8.2_external"] = {
            version = "0.8.2",
            defines = {
                "ASIO_STANDALONE",
                "_WEBSOCKETPP_CPP11_STL_"
            },
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/w/websocketpp/0.8.2/a995c8150a01414fa906fc12e9273dbe/include"
            }
        },
        ["xmake::libxcb_27d2233a10b34ff5971001208db29e2c_release_1.17.0_external"] = false,
        ["xmake::libxfixes_31f0eaa885a84ef6b740f6ed04fea3e3_release_6.0.1_external"] = false,
        ["xmake::libxi_2f225dfd5e274482ae09ba4da0c185c5_release_1.8.2_external"] = false,
        ["xmake::libx11_5b3492e889e848b2b511e3f3031c5538_release_1.8.12_external"] = false,
        ["xmake::asio_534f961af8274a53a3ee065d110c24f3_release_1.32.0_external"] = {
            version = "1.32.0",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/a/asio/1.32.0/534f961af8274a53a3ee065d110c24f3/include"
            },
            license = "BSL-1.0"
        },
        ["xmake::libxext_5a0d83a7724446099fee649c3d9afd32_release_1.3.6_external"] = false,
        ["xmake::openssl_6c51ab6278e2479b883dffafac69fdaf_release_1.1.1-w_external"] = {
            version = "1.1.1-w",
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/include"
            },
            links = {
                "ssl",
                "crypto"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libssl.a",
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib/libcrypto.a"
            },
            license = "Apache-2.0",
            syslinks = {
                "pthread",
                "dl"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/lib"
            }
        },
        ["xmake::xcb-proto_eafb62cb3f37412ebe648eb0ab4b4b35_release_1.17.0_external"] = false,
        ["xmake::libffi_46686cf534074f4ebf90e4ab1f9662dd_release_3.4.8_external"] = {
            version = "3.4.8",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/include"
            },
            license = "MIT",
            libfiles = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib/libffi.a"
            },
            static = true,
            links = {
                "ffi"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/l/libffi/3.4.8/46686cf534074f4ebf90e4ab1f9662dd/lib"
            }
        },
        ["xmake::opengl_4e0143c97b65425b855ad5fd03038b6a_release_latest_external"] = false,
        ["xmake::ca-certificates_e9ba281ae183402a80dc072d4d571c20_release_20250131_external"] = {
            version = "20250131"
        },
        ["xmake::libxinerama_e539e5b3575342a89c7a931295fe40c5_release_1.1.5_external"] = false,
        ["xmake::libxau_f406943012fc443596a2e2e23215839d_release_1.0.12_external"] = false,
        ["xmake::xtrans_588d85a9c99148b8ae87dee11b39ab40_release_1.6.0_external"] = false,
        ["xmake::boost_6c0072489f1c45408d6ab87e5144f2cd_release_1.88.0_external"] = {
            version = "1.88.0",
            static = true,
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/include"
            },
            links = {
                "boost_filesystem",
                "boost_thread",
                "boost_date_time",
                "boost_container",
                "boost_chrono",
                "boost_atomic"
            },
            libfiles = {
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_filesystem.a",
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_thread.a",
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_date_time.a",
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_container.a",
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_chrono.a",
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib/libboost_atomic.a"
            },
            license = "BSL-1.0",
            syslinks = {
                "pthread",
                "dl"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib"
            }
        },
        ["xmake::zlib_994fafa590ed48ac9f71516cc846d155_release_v1.3.1_external"] = {
            version = "v1.3.1",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include"
            },
            license = "zlib",
            libfiles = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib/libz.a"
            },
            static = true,
            links = {
                "z"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/lib"
            }
        },
        ["xmake::libxdmcp_1e5cac3a0ef94e939a1a5bb7efb8c24f_release_1.1.5_external"] = false,
        ["xmake::libpthread-stubs_e6c09af522624eb1ae863fe21c4aa62c_release_0.5_external"] = false,
        ["xmake::xorgproto_3a1682704c034fbf87583b32455dde94_release_2023.2_external"] = false,
        ["xmake::imgui_48fcfa11eb6f4eb88d02a0796b198e27_release_v1.92.0_external"] = {
            version = "v1.92.0",
            sysincludedirs = {
                "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include",
                "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui",
                "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends",
                "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp"
            },
            license = "MIT",
            libfiles = {
                "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/lib/libimgui.a"
            },
            static = true,
            links = {
                "imgui"
            },
            linkdirs = {
                "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/lib"
            }
        },
        ["xmake::libxcursor_665b359dc13241789f6ca7a586bc5127_release_1.2.3_external"] = false,
        ["xmake::libxrandr_a36fcab100ed449e86944351e00c0384_release_1.5.4_external"] = false,
        ["xmake::util-macros_21ebacb0450b4b16b06b586ecc142d3f_release_1.20.0_external"] = false,
        ["xmake::libxrender_75df963310db406385383085b95b222e_release_0.9.12_external"] = false
    },
    find_program_envs_arch_x86_64_plat_linux_checktoolcxx = {
        ["g++"] = "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++"
    }
}