[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HeartRate Viewer", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Isrc", "-I/nix/store/*/include/libnotify", "-I/nix/store/*/include/glib-2.0", "-I/nix/store/*/lib/glib-2.0/include", "-DNDEBUG", "-o", "build/.objs/heart-rate-viewer/linux/x86_64/release/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/HeartRate Viewer", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Isrc", "-I/nix/store/*/include/libnotify", "-I/nix/store/*/include/glib-2.0", "-I/nix/store/*/lib/glib-2.0/include", "-DNDEBUG", "-o", "build/.objs/heart-rate-viewer/linux/x86_64/release/src/HeartRateMonitor.cpp.o", "src/HeartRateMonitor.cpp"], "file": "src/HeartRateMonitor.cpp"}]