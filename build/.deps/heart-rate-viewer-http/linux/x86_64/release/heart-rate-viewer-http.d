{
    values = {
        "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        {
            "-m64",
            "-s",
            "-lcpr",
            "-lcurl",
            "-lssl",
            "-lcrypto",
            "-lpthread",
            "-ldl"
        }
    },
    files = {
        "build/.objs/heart-rate-viewer-http/linux/x86_64/release/src/simple_main_http.cpp.o",
        "build/.objs/heart-rate-viewer-http/linux/x86_64/release/src/HeartRateMonitorHTTP.cpp.o"
    }
}