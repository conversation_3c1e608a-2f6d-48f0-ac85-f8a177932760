{
    values = {
        "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        {
            "-m64",
            "-L/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/lib",
            "-L/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib",
            "-L/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib",
            "-L/nix/store/1ar5dnd5hqiyaxi6whj52vkd2bf0h7n8-libXinerama-1.1.5/lib",
            "-L/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib",
            "-L/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib",
            "-L/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib",
            "-L/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib",
            "-L/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib",
            "-L/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib",
            "-L/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib",
            "-L/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib",
            "-L/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/lib",
            "-s",
            "-limgui",
            "-lOpenGL",
            "-lXrender",
            "-lXi",
            "-lXfixes",
            "-lXext",
            "-lxcb",
            "-lXau",
            "-lboost_filesystem",
            "-lboost_thread",
            "-lboost_date_time",
            "-lboost_container",
            "-lboost_chrono",
            "-lboost_atomic",
            "-lGL",
            "-lGLEW",
            "-lglfw",
            "-lssl",
            "-lcrypto",
            "-lX11",
            "-lXrandr",
            "-lXinerama",
            "-lXcursor",
            "-lnotify",
            "-lglib-2.0",
            "-lgobject-2.0",
            "-lpthread",
            "-ldl"
        }
    },
    files = {
        "build/.objs/heart-rate-viewer/linux/x86_64/release/src/main.cpp.o",
        "build/.objs/heart-rate-viewer/linux/x86_64/release/src/HeartRateMonitor.cpp.o"
    }
}