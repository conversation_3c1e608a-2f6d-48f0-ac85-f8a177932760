{
    depfiles = "main.o: src/main.cpp src/HeartRateMonitor.h\
",
    depfiles_format = "gcc",
    files = {
        "src/main.cpp"
    },
    values = {
        "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        {
            "-m64",
            "-fvisibility=hidden",
            "-fvisibility-inlines-hidden",
            "-O3",
            "-std=c++17",
            "-Isrc",
            "-I/nix/store/fhnw493azh9797a487aswpz3mrv5xpf9-libnotify-0.8.6-dev/include",
            "-I/nix/store/0y0z99w8jjw8s8nw0xxm830jdx6zw3l1-gdk-pixbuf-2.42.12-dev/include/gdk-pixbuf-2.0",
            "-I/nix/store/g9py24z458m8pxfhdzcyx15yyj26idfa-glib-2.84.3-dev/include",
            "-I/nix/store/g9py24z458m8pxfhdzcyx15yyj26idfa-glib-2.84.3-dev/include/glib-2.0",
            "-I/nix/store/syzi2bpl8j599spgvs20xjkjzcw758as-glib-2.84.3/lib/glib-2.0/include",
            "-DASIO_STANDALONE",
            "-D_WEBSOCKETPP_CPP11_STL_",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp",
            "-isystem",
            "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include",
            "-isystem",
            "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include",
            "-isystem",
            "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include",
            "-isystem",
            "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include",
            "-isystem",
            "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include",
            "-isystem",
            "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include",
            "-isystem",
            "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include",
            "-isystem",
            "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include",
            "-isystem",
            "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include",
            "-isystem",
            "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include",
            "-isystem",
            "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include",
            "-isystem",
            "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/w/websocketpp/0.8.2/a995c8150a01414fa906fc12e9273dbe/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/a/asio/1.32.0/534f961af8274a53a3ee065d110c24f3/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/b/boost/1.88.0/6c0072489f1c45408d6ab87e5144f2cd/include",
            "-DNDEBUG"
        }
    }
}