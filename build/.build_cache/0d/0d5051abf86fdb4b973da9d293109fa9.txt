{
    errdata = "\27[01m\27[Ksrc/simple_main_http.cpp:\27[m\27[K In function ‘\27[01m\27[Kint\27[01;32m\27[K main\27[m\27[K()\27[m\27[K’:\
\27[01m\27[Ksrc/simple_main_http.cpp:46:15:\27[m\27[K \27[01;35m\27[Kwarning: \27[m\27[Kignoring return value of ‘\27[01m\27[Kint\27[01;32m\27[K system\27[m\27[K(const char*)\27[m\27[K’ declared with attribute ‘\27[01m\27[Kwarn_unused_result\27[m\27[K’ [\27[01;35m\27[K-Wunused-result\27[m\27[K]\
   46 |         \27[01;35m\27[Ksystem(\"clear\")\27[m\27[K;\
      |         \27[01;35m\27[K~~~~~~^~~~~~~~~\27[m\27[K\
At global scope:\
\27[01m\27[Kcc1plus:\27[m\27[K \27[01;36m\27[Knote: \27[m\27[Kunrecognized command-line option ‘\27[01m\27[K-Wno-gnu-line-marker\27[m\27[K’ may have been intended to silence earlier diagnostics\
"
}